using Riok.Mapperly.Abstractions;
using VirtuManager.Auth.Application.ViewModels;
using VirtuManager.Auth.Domain.Entities;
using DomainUserRole = VirtuManager.Auth.Domain.Enums.UserRole;
using GrpcUser = VirtuManager.Auth.Grpc.User;
using GrpcUserRole = VirtuManager.Auth.Grpc.UserRole;

namespace VirtuManager.Auth.Infrastructure.Mappers.Users;

[Mapper]
public partial class GrpcUserMappingProfile
{
    /// <summary>
    /// Maps Domain User entity to gRPC User model
    /// </summary>
    [MapProperty(nameof(User.Id), nameof(GrpcUser.Guid))]
    [MapperIgnoreSource(nameof(User.PasswordHash))]
    [MapperIgnoreSource(nameof(User.RefreshTokenHash))]
    [MapperIgnoreSource(nameof(User.CreatedAt))]
    [MapperIgnoreSource(nameof(User.UpdatedAt))]
    [MapperIgnoreSource(nameof(User.LastLoginAt))]
    [MapperIgnoreSource(nameof(User.LastPasswordChangeAt))]
    public partial GrpcUser MapToGrpcModel(User user);

    /// <summary>
    /// Maps UserViewModel to gRPC User model
    /// </summary>
    [MapProperty(nameof(UserViewModel.Id), nameof(GrpcUser.Guid))]
    [MapperIgnoreSource(nameof(UserViewModel.CreatedAt))]
    [MapperIgnoreSource(nameof(UserViewModel.UpdatedAt))]
    [MapperIgnoreSource(nameof(UserViewModel.LastLoginAt))]
    [MapperIgnoreSource(nameof(UserViewModel.LastPasswordChangeAt))]
    public partial GrpcUser MapViewModelToGrpcModel(UserViewModel userViewModel);
    
    /// <summary>
    /// Custom mapping for Guid to string conversion
    /// </summary>
    private string MapGuidToString(Guid guid) => guid.ToString();

    /// <summary>
    /// Custom mapping for string to Guid conversion
    /// </summary>
    private Guid MapStringToGuid(string guidString) => Guid.Parse(guidString);

    /// <summary>
    /// Custom mapping for Domain UserRole to gRPC UserRole
    /// </summary>
    private GrpcUserRole MapDomainRoleToGrpcRole(DomainUserRole domainRole) => domainRole switch
    {
        DomainUserRole.Regular => GrpcUserRole.Regular,
        DomainUserRole.Developer => GrpcUserRole.Developer,
        DomainUserRole.Admin => GrpcUserRole.Admin,
        _ => GrpcUserRole.Regular
    };

    /// <summary>
    /// Custom mapping for gRPC UserRole to Domain UserRole
    /// </summary>
    private DomainUserRole MapGrpcRoleToDomainRole(GrpcUserRole grpcRole) => grpcRole switch
    {
        GrpcUserRole.Regular => DomainUserRole.Regular,
        GrpcUserRole.Developer => DomainUserRole.Developer,
        GrpcUserRole.Admin => DomainUserRole.Admin,
        _ => DomainUserRole.Regular
    };
}