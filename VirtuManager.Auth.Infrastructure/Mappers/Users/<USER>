using VirtuManager.Auth.Domain.Entities;
using VirtuManager.Auth.Domain.Mapping;
using GrpcUser = VirtuManager.Auth.Grpc.User;

namespace VirtuManager.Auth.Infrastructure.Mappers.Users;

public class UserGrpcMapper : IMapper<GrpcUser, User>
{
    private readonly GrpcUserMappingProfile _mapper;

    public UserGrpcMapper(GrpcUserMappingProfile mapper)
    {
        ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
        _mapper = mapper;
    }

    public GrpcUser MapToModel(User source) => _mapper.MapToGrpcModel(source);

    public List<GrpcUser> MapToModel(IEnumerable<User> sources)
        => sources.Select(x => _mapper.MapToGrpcModel(x)).ToList();
}