using VirtuManager.Auth.Domain.Entities;
using VirtuManager.Auth.Domain.Mapping;
using GrpcUser = VirtuManager.Auth.Grpc.User;

namespace VirtuManager.Auth.Infrastructure.Mappers.Users;

public class UserGrpcReverseMapper : IMapper<User, GrpcUser>
{
    private readonly GrpcUserMappingProfile _mapper;

    public UserGrpcReverseMapper(GrpcUserMappingProfile mapper)
    {
        ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
        _mapper = mapper;
    }

    public User MapToModel(GrpcUser source) => _mapper.MapToDomainEntity(source);

    public List<User> MapToModel(IEnumerable<GrpcUser> sources)
        => sources.Select(x => _mapper.MapToDomainEntity(x)).ToList();
}
